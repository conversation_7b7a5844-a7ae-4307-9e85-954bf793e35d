/**
 * Timeline and Event Card Styles
 * Modular CSS for timeline and event card components
 */

/* Timeline container */
.timeline-container {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* Allow flex item to shrink below content size */
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0.35rem;
  width: 2px;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
  height: 100%;
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
}

.timeline-date-marker {
  position: relative;
  margin-bottom: 1rem;
  margin-left: -2rem;
  z-index: 2;
}

.timeline-date {
  margin-bottom: 0.5rem;
  margin-left: 2rem;
  display: flex;
  align-items: center;
}

.timeline-date .badge {
  position: relative;
  z-index: 3;
}

.timeline-content {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 0.5rem;
  width: 100%;
}

.timeline-content-wrapper {
  position: relative;
  overflow: visible;
}

.timeline-content.card {
  border: none !important;
  overflow: hidden;
}

.event-card-link {
  display: block;
  z-index: 1;
}

/* Timeline image styling */
.timeline-image-container {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  overflow: hidden;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.timeline-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  display: block;
}

/* Dropdown menu styling */
.dropdown-menu {
  z-index: 1060 !important;
  overflow: visible;
  min-width: 10rem;
  background-color: #ffffff !important;
  border: 1px solid rgba(0, 0, 0, .15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, .15) !important;
}

.dropdown-item {
  z-index: 1060 !important;
  position: relative;
}

/* Card styling */
.card {
  border-radius: 0.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Journey info text styling */
.journey-info .text-uppercase {
  letter-spacing: 0.5px;
  font-size: 0.7rem;
}

/* Empty state styling */
.empty-state {
  max-width: 400px;
  padding: 2rem;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-state-icon {
  margin: 0 auto;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile responsive styles */
@media (max-width: 767.98px) {
  .timeline {
    padding-left: 1.5rem;
  }

  .timeline-image-container {
    width: 80px;
    height: 80px;
  }

  .timeline-image {
    width: 80px;
    height: 80px;
  }

  .col-md-4 .card-body {
    max-height: none;
  }

  .journey-info .mb-4:nth-child(3) {
    max-height: 150px;
  }

  .timeline-content-wrapper .event-card-link>div {
    border: none !important;
    box-shadow: none !important;
  }

  .timeline-content.card,
  .timeline-content .card-body {
    border: none !important;
    box-shadow: none !important;
  }
}

/* Cover image styling */
.cover-image-container {
  margin-top: 15px;
}

.cover-image {
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f9fa;
  height: 200px;
  /* Fixed height for consistent display */
}

.cover-image-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.cover-image-placeholder {
  height: 200px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.cover-image-controls {
  z-index: 5;
}

.cover-image:hover .cover-image-controls {
  opacity: 1;
}

/* Clickable cover image styling */
.cover-image-clickable {
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cover-image-clickable:hover {
  transform: scale(1.02);
}

.cover-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.cover-image-clickable:hover .cover-image-overlay {
  opacity: 1;
}

.cover-image-overlay i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.cover-image-overlay span {
  font-size: 0.875rem;
}

/* Simple Image modal styling */
.image-modal .modal-dialog {
  max-width: 90vw;
  max-height: 90vh;
}

.image-modal .modal-content {
  background: transparent;
  border: none;
  box-shadow: none;
}

.image-modal .modal-header {
  border: none;
  padding: 1rem;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1050;
  background: none;
}

.image-modal .modal-title {
  display: none;
}

.image-modal .modal-body {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.image-modal .modal-footer {
  display: none;
}

.image-modal img {
  max-width: 100%;
  max-height: 85vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Mobile responsiveness for image modal */
@media (max-width: 768px) {
  .image-modal .modal-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .image-modal img {
    max-height: 80vh;
  }
}

/* Enhanced cover image controls */
.cover-image-controls {
  opacity: 0;
  transition: opacity 0.3s ease;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 4px;
}

.cover-image-controls .btn {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.cover-image-controls .btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Loading state for cover image upload */
.cover-image-uploading {
  position: relative;
  overflow: hidden;
}

.cover-image-uploading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.cover-image-uploading::before {
  content: 'Uploading...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  z-index: 11;
}
