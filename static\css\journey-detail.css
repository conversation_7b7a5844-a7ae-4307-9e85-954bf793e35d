/**
 * Journey Detail Page Styles
 * Modular CSS for journey detail page specific styling
 */

/* Username link styling */
.username-link {
  transition: all 0.2s ease;
  padding: 2px 6px;
  border-radius: 4px;
  margin: -2px -6px; 
  display: inline; 
}

.username-link:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transform: translateY(-1px);
}

/* Profile image styling */
.rounded-circle,
.rounded-circle img {
  position: relative;
  z-index: 1;
}

/* Map preview container */
.map-preview-container {
  position: relative;
  display: inline-block;
  z-index: 2;
}

.view-map-link {
  text-decoration: none;
  color: black;
}

.view-map-link:hover {
  text-decoration: none;
  color: grey;
}

/* Map preview popup */
.map-preview {
  position: absolute;
  top: 100%;
  right: 0;
  width: 300px;
  height: 200px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 1080;
  overflow: hidden;
  text-align: center;
}

.map-preview.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.map-preview::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 20px;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #c8e6c9 0%, #fff59d 50%);
  border-left: 2px solid #e0e0e0;
  border-top: 2px solid #e0e0e0;
  transform: rotate(45deg);
  z-index: 1080;
}

/* Journey Map Styling */
.journey-map-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#journeyMap {
  border-radius: 8px;
}

.leaflet-popup-content {
  margin: 8px 12px;
}

.event-popup h6 {
  color: #495057;
  margin-bottom: 8px;
}

.location-popup h6 {
  color: #495057;
  margin-bottom: 8px;
}

/* Menu button styling - matching event detail design */
.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 50%;
  color: #495057;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.menu-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
}

/* Follow button styling - matching event detail theme */
.btn[data-action="toggle-follow"] {
  transition: all 0.2s ease;
  border-width: 1px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 16px;
}

.btn[data-action="toggle-follow"]:not(.btn-primary) {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

.btn[data-action="toggle-follow"]:not(.btn-primary):hover {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn[data-action="toggle-follow"].btn-primary {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.btn[data-action="toggle-follow"].btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn[data-action="toggle-follow"]:active {
  transform: translateY(0);
}

.btn[data-action="toggle-follow"] i.bi-heart-fill {
  color: #dc3545;
}

/* Button group spacing - modern approach */
.d-flex.gap-2 {
  gap: 8px;
}

/* Responsive follow button text */
@media (max-width: 575.98px) {
  .btn[data-action="toggle-follow"] .btn-text {
    display: none !important;
  }

  .btn[data-action="toggle-follow"] {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
}

/* Event map styling */
.event-map-container {
  transition: all 0.3s ease;
}

.event-location-button {
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 6px;
}

.event-location-button:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transform: translateY(-1px);
}

.event-location-button .bi-chevron-down {
  font-size: 0.75rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.event-location-button:hover .bi-chevron-down {
  opacity: 1;
}

.map-toggle-icon {
  transition: transform 0.2s ease;
}

.map-toggle-icon.rotate-180 {
  transform: rotate(180deg);
}

.event-main-content {
  transition: background-color 0.2s ease;
}

.event-main-content:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
  border-color: rgba(78, 107, 255, 0.3);
}

.event-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Cover image styling */
.cover-image-img {
  width: 80%;
  height: 100px;
  border-radius: 8px;
  display: block;
}

/* Journey container layout */
#journeyContainer {
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: row;
}

#journeyContainer .col-md-4,
#journeyContainer .col-md-8 {
  display: flex;
  flex-direction: column;
}

#journeyContainer .card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

#journeyContainer .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.event-content.d-flex.flex-column.justify-content-center.align-items-center {
  height: 100%;
  flex: 1;
}

.col-md-4 .card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
}

.journey-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.journey-info .mb-4:nth-child(1),
.journey-info .mb-4:nth-child(2) {
  flex-shrink: 0;
}

.journey-info .mb-4:nth-child(3) {
  min-height: 0;
  overflow-y: auto;
  padding-right: 5px;
}

/* Scrollbar styling */
.journey-info .mb-4:nth-child(3)::-webkit-scrollbar {
  width: 6px;
}

.journey-info .mb-4:nth-child(3)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.journey-info .mb-4:nth-child(3)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.journey-info .mb-4:nth-child(3)::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* For Firefox */
.journey-info .mb-4:nth-child(3) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
