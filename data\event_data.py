from typing import Dict, List, Optional, Any
from datetime import datetime
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def create_event(journey_id: int, location_id: int, title: str, description: str,
                start_datetime: datetime, end_datetime: Optional[datetime] = None) -> int:
    """Create a new event.

    Args:
        journey_id: ID of the journey.
        location_id: ID of the location.
        title: Event title.
        description: Event description.
        start_datetime: Start date and time.
        end_datetime: End date and time (optional).

    Returns:
        int: ID of the newly created event.
    """
    logger.info(f"Creating event '{title}' for journey ID: {journey_id}, location ID: {location_id}")
    query = """
    INSERT INTO events
    (journey_id, location_id, title, description, start_datetime, end_datetime)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    event_id = execute_query(
        query,
        (journey_id, location_id, title, description, start_datetime, end_datetime)
    )
    logger.info(f"Created event with ID: {event_id}")
    return event_id

def get_event(event_id: int) -> Optional[Dict[str, Any]]:
    """Get an event by ID.

    Args:
        event_id: The ID of the event to retrieve.

    Returns:
        Dict[str, Any]: Event data including location name if found, None otherwise.
    """
    logger.debug(f"Getting event with ID: {event_id}")
    query = """
    SELECT e.*, l.name as location_name, l.latitude, l.longitude
    FROM events e
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE e.id = %s
    """
    result = execute_query(query, (event_id,), fetch_one=True)
    logger.debug(f"Event lookup result: {'Found' if result else 'Not found'}")
    return result

def get_journey_events(journey_id: int) -> List[Dict[str, Any]]:
    """Get all events for a journey, ordered by start date (oldest first).

    Args:
        journey_id: The ID of the journey to get events for.

    Returns:
        List[Dict[str, Any]]: List of event records.
    """
    logger.debug(f"Getting all events for journey ID: {journey_id}")
    query = """
    SELECT e.*, l.name as location_name, l.latitude, l.longitude,
           (
             SELECT image_filename
             FROM event_images
             WHERE event_id = e.id
             ORDER BY is_primary DESC, id ASC LIMIT 1
           ) image
    FROM events e
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE e.journey_id = %s
    ORDER BY e.start_datetime ASC
    """
    results = execute_query(query, (journey_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} events for journey ID: {journey_id}")
    return results or []

def get_user_private_events(user_id: int) -> List[Dict[str, Any]]:
    """Get all events for a user by joining journeys and events tables.

    Args:
        user_id: The ID of the user whose events we want to retrieve.

    Returns:
        List[Dict[str, Any]]: List of event records.
    """
    logger.debug(f"Getting all events for user ID: {user_id}")
    query = """
    SELECT e.*, l.id AS location_id,
        l.name AS location_name, l.latitude, l.longitude,
        j.user_id as user_id,
           (
             SELECT image_filename
             FROM event_images
             WHERE event_id = e.id
             ORDER BY id ASC LIMIT 1
           ) AS photo
    FROM journeys j
    JOIN events e ON e.journey_id = j.id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE j.user_id = %s
    ORDER BY e.start_datetime ASC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} events for user ID: {user_id}")
    return results or []

def get_user_public_events(user_id: int) -> List[Dict[str, Any]]:
    """Get all public events for a user by joining journeys and events tables where journey.visibility = 'public'.

    Args:
        user_id: The ID of the user whose public events we want to retrieve.

    Returns:
        List[Dict[str, Any]]: List of public event records.
    """
    logger.debug(f"Getting all public events for user ID: {user_id}")
    query = """
    SELECT e.*, l.id AS location_id,
        l.name AS location_name, l.latitude, l.longitude,
        j.user_id as user_id,
           (
             SELECT image_filename
             FROM event_images
             WHERE event_id = e.id
             ORDER BY id ASC LIMIT 1
           ) AS image
    FROM journeys j
    JOIN events e ON e.journey_id = j.id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE j.user_id = %s AND j.visibility = 'public'
    ORDER BY e.start_datetime ASC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} public events for user ID: {user_id}")
    return results or []



def update_event(event_id: int, title: Optional[str] = None,
                description: Optional[str] = None,
                start_datetime: Optional[datetime] = None,
                end_datetime: Optional[datetime] = None,
                location_id: Optional[int] = None) -> int:
    """Update event details.

    Args:
        event_id: The ID of the event to update.
        title: Optional new title for the event.
        description: Optional new description for the event.
        start_datetime: Optional new start date and time for the event.
        end_datetime: Optional new end date and time for the event.
        location_id: Optional new location ID for the event.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating event with ID: {event_id}")
    updates = []
    params = []

    if title is not None:
        updates.append("title = %s")
        params.append(title)
        logger.debug(f"Updating title to: {title}")

    if description is not None:
        updates.append("description = %s")
        params.append(description)
        logger.debug("Updating description")

    if start_datetime is not None:
        updates.append("start_datetime = %s")
        params.append(start_datetime)
        logger.debug(f"Updating start_datetime to: {start_datetime}")

    if end_datetime is not None:
        updates.append("end_datetime = %s")
        params.append(end_datetime)
        logger.debug(f"Updating end_datetime to: {end_datetime}")

    if location_id is not None:
        updates.append("location_id = %s")
        params.append(location_id)
        logger.debug(f"Updating location_id to: {location_id}")

    if not updates:
        logger.warning(f"No updates provided for event ID: {event_id}")
        return 0

    query = f"""
    UPDATE events
    SET {', '.join(updates)}
    WHERE id = %s
    """

    params.append(event_id)
    rows_affected = execute_query(query, params)
    logger.info(f"Updated event ID: {event_id}, rows affected: {rows_affected}")
    return rows_affected

def delete_event(event_id: int) -> int:
    """Delete an event.

    Args:
        event_id: The ID of the event to delete.

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting event with ID: {event_id}")
    query = """
    DELETE FROM events
    WHERE id = %s
    """
    rows_affected = execute_query(query, (event_id,))
    logger.info(f"Deleted event with ID: {event_id}, rows affected: {rows_affected}")
    return rows_affected

def get_events_by_location(location_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all events at a specific location.

    Args:
        location_id: The ID of the location to get events for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of event records.
    """
    logger.debug(f"Getting events for location ID: {location_id}, limit: {limit}, offset: {offset}")
    query = """
    SELECT e.*, j.title as journey_title, j.user_id, j.visibility, j.is_hidden,
           (SELECT image_filename FROM event_images WHERE event_id = e.id AND is_primary = TRUE LIMIT 1) as primary_image
    FROM events e
    JOIN journeys j ON e.journey_id = j.id
    WHERE e.location_id = %s
    ORDER BY e.start_datetime DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (location_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} events for location ID: {location_id}")
    return results or []

def check_event_exists(event_id: int) -> bool:
    """Check if an event exists.

    Args:
        event_id: The ID of the event to check.

    Returns:
        bool: True if the event exists, False otherwise.
    """
    logger.debug(f"Checking if event ID: {event_id} exists")
    query = """
    SELECT COUNT(*) as count
    FROM events
    WHERE id = %s
    """
    result = execute_query(query, (event_id,), fetch_one=True)
    exists = result['count'] > 0 if result else False
    logger.debug(f"Event ID: {event_id} exists: {exists}")
    return exists

def check_event_journey(event_id: int, journey_id: int) -> bool:
    """Check if an event belongs to a specific journey.

    Args:
        event_id: The ID of the event to check.
        journey_id: The ID of the journey to check against.

    Returns:
        bool: True if the event belongs to the journey, False otherwise.
    """
    logger.debug(f"Checking if event ID: {event_id} belongs to journey ID: {journey_id}")
    query = """
    SELECT COUNT(*) as count
    FROM events
    WHERE id = %s AND journey_id = %s
    """
    result = execute_query(query, (event_id, journey_id), fetch_one=True)
    belongs = result['count'] > 0 if result else False
    logger.debug(f"Event ID: {event_id} belongs to journey ID: {journey_id}: {belongs}")
    return belongs

# Event Images methods

def add_event_image(event_id: int, image_filename: str, caption: Optional[str] = None,
                   is_primary: bool = False) -> int:
    """Add an image to an event.

    Args:
        event_id: The ID of the event to add an image to.
        image_filename: The filename of the image.
        caption: Optional caption for the image.
        is_primary: Whether this is the primary image for the event.

    Returns:
        int: ID of the newly created image.
    """
    logger.info(f"Adding image to event ID: {event_id}, path: {image_filename}, primary: {is_primary}")

    # If setting as primary, unset any existing primary image
    if is_primary:
        unset_query = """
        UPDATE event_images
        SET is_primary = FALSE
        WHERE event_id = %s AND is_primary = TRUE
        """
        execute_query(unset_query, (event_id,))
        logger.debug(f"Unset existing primary images for event ID: {event_id}")

    # Insert the new image
    query = """
    INSERT INTO event_images
    (event_id, image_filename, caption, is_primary)
    VALUES (%s, %s, %s, %s)
    """
    image_id = execute_query(query, (event_id, image_filename, caption, is_primary))
    logger.info(f"Added image with ID: {image_id} to event ID: {event_id}")
    return image_id

def get_event_images(event_id: int) -> List[Dict[str, Any]]:
    """Get all images for an event.

    Args:
        event_id: The ID of the event to get images for.

    Returns:
        List[Dict[str, Any]]: List of image records.
    """
    logger.debug(f"Getting all images for event ID: {event_id}")
    query = """
    SELECT id, event_id, image_filename, caption, is_primary, created_at, updated_at
    FROM event_images
    WHERE event_id = %s
    ORDER BY is_primary DESC, created_at ASC
    """
    results = execute_query(query, (event_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} images for event ID: {event_id}")
    return results or []

def get_event_image(image_id: int) -> Optional[Dict[str, Any]]:
    """Get an image by ID.

    Args:
        image_id: The ID of the image to retrieve.

    Returns:
        Dict[str, Any]: Image data if found, None otherwise.
    """
    logger.debug(f"Getting image with ID: {image_id}")
    query = """
    SELECT id, event_id, image_filename, caption, is_primary, created_at, updated_at
    FROM event_images
    WHERE id = %s
    """
    result = execute_query(query, (image_id,), fetch_one=True)
    logger.debug(f"Image lookup result: {'Found' if result else 'Not found'}")
    return result

def update_event_image(image_id: int, image_filename: Optional[str] = None,
                       caption: Optional[str] = None, is_primary: Optional[bool] = None) -> int:
    """Update an event image.

    Args:
        image_id: The ID of the image to update.
        image_filename: Optional new file path for the image.
        caption: Optional new caption for the image.
        is_primary: Optional new primary status for the image.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating image with ID: {image_id}")
    updates = []
    params = []

    # Get the event_id for this image (needed if changing primary status)
    event_id = None
    if is_primary is not None and is_primary:
        image_data = get_event_image(image_id)
        if image_data:
            event_id = image_data['event_id']

    if image_filename is not None:
        updates.append("image_filename = %s")
        params.append(image_filename)
        logger.debug(f"Updating image filename to: {image_filename}")

    if caption is not None:
        updates.append("caption = %s")
        params.append(caption)
        logger.debug(f"Updating caption")

    if is_primary is not None:
        updates.append("is_primary = %s")
        params.append(is_primary)
        logger.debug(f"Updating is_primary to: {is_primary}")

        # If setting as primary, unset any existing primary images
        if is_primary and event_id:
            unset_query = """
            UPDATE event_images
            SET is_primary = FALSE
            WHERE event_id = %s AND id != %s AND is_primary = TRUE
            """
            execute_query(unset_query, (event_id, image_id))
            logger.debug(f"Unset existing primary images for event ID: {event_id}")

    if not updates:
        logger.warning(f"No updates provided for image ID: {image_id}")
        return 0

    query = f"""
    UPDATE event_images
    SET {', '.join(updates)}
    WHERE id = %s
    """

    params.append(image_id)
    rows_affected = execute_query(query, params)
    logger.info(f"Updated image ID: {image_id}, rows affected: {rows_affected}")
    return rows_affected

def delete_event_image(image_id: int) -> int:
    """Delete an event image.

    Args:
        image_id: The ID of the image to delete.

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting image with ID: {image_id}")

    # Check if this is a primary image
    image_data = get_event_image(image_id)
    if image_data and image_data['is_primary']:
        # If this is the primary image, select another image to be primary
        event_id = image_data['event_id']
        query = """
        SELECT id FROM event_images
        WHERE event_id = %s AND id != %s
        ORDER BY created_at ASC LIMIT 1
        """
        next_image = execute_query(query, (event_id, image_id), fetch_one=True)

        if next_image:
            # Set the next image as primary
            update_query = """
            UPDATE event_images
            SET is_primary = TRUE
            WHERE id = %s
            """
            execute_query(update_query, (next_image['id'],))
            logger.debug(f"Set image ID: {next_image['id']} as new primary for event ID: {event_id}")

    # Delete the image
    query = """
    DELETE FROM event_images
    WHERE id = %s
    """
    rows_affected = execute_query(query, (image_id,))
    logger.info(f"Deleted image with ID: {image_id}, rows affected: {rows_affected}")
    return rows_affected

def get_primary_event_image(event_id: int) -> Optional[Dict[str, Any]]:
    """Get the primary image for an event.

    Args:
        event_id: The ID of the event to get the primary image for.

    Returns:
        Dict[str, Any]: Primary image data if found, None otherwise.
    """
    logger.debug(f"Getting primary image for event ID: {event_id}")
    query = """
    SELECT id, event_id, image_filename, caption, is_primary, created_at, updated_at
    FROM event_images
    WHERE event_id = %s AND is_primary = TRUE
    LIMIT 1
    """
    result = execute_query(query, (event_id,), fetch_one=True)

    # If no primary image is set, get the first image
    if not result:
        logger.debug(f"No primary image found, getting first image for event ID: {event_id}")
        query = """
        SELECT id, event_id, image_filename, caption, is_primary, created_at, updated_at
        FROM event_images
        WHERE event_id = %s
        ORDER BY created_at ASC
        LIMIT 1
        """
        result = execute_query(query, (event_id,), fetch_one=True)

    logger.debug(f"Primary image lookup result: {'Found' if result else 'Not found'}")
    return result

def set_primary_event_image(event_id: int, image_id: int) -> int:
    """Set an image as the primary image for an event.

    Args:
        event_id: The ID of the event.
        image_id: The ID of the image to set as primary.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Setting image ID: {image_id} as primary for event ID: {event_id}")

    # First, unset any existing primary images
    unset_query = """
    UPDATE event_images
    SET is_primary = FALSE
    WHERE event_id = %s AND is_primary = TRUE
    """
    execute_query(unset_query, (event_id,))

    # Then set the new primary image
    query = """
    UPDATE event_images
    SET is_primary = TRUE
    WHERE id = %s AND event_id = %s
    """
    rows_affected = execute_query(query, (image_id, event_id))
    logger.info(f"Set image ID: {image_id} as primary for event ID: {event_id}, rows affected: {rows_affected}")
    return rows_affected

def count_event_images(event_id: int) -> int:
    """Count the number of images for an event.

    Args:
        event_id: The ID of the event to count images for.

    Returns:
        int: Number of images for the event.
    """
    logger.debug(f"Counting images for event ID: {event_id}")
    query = """
    SELECT COUNT(*) as count
    FROM event_images
    WHERE event_id = %s
    """
    result = execute_query(query, (event_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} images for event ID: {event_id}")
    return count

def add_event_destination(event_id: int, destination_location_id: int) -> int:
    """Add a destination to an event.

    Args:
        event_id: The ID of the event to add a destination to.
        destination_location_id: The ID of the destination location.

    Returns:
        int: ID of the newly created destination.
    """
    logger.info(f"Adding destination location ID: {destination_location_id} to event ID: {event_id}")
    query = """
    INSERT INTO event_destinations
    (event_id, destination_location_id)
    VALUES (%s, %s)
    """
    destination_id = execute_query(query, (event_id, destination_location_id))
    logger.info(f"Added destination with ID: {destination_id} to event ID: {event_id}")
    return destination_id

def get_event_destination(event_id: int) -> Optional[Dict[str, Any]]:
    """Get the destination for an event.

    Args:
        event_id: The ID of the event to get the destination for.

    Returns:
        Dict[str, Any]: Destination data if found, None otherwise.
    """
    logger.debug(f"Getting destination for event ID: {event_id}")
    query = """
    SELECT ed.*, l.name as destination_name
    FROM event_destinations ed
    JOIN locations l ON ed.destination_location_id = l.id
    WHERE ed.event_id = %s
    """
    result = execute_query(query, (event_id,), fetch_one=True)
    logger.debug(f"Destination lookup result: {'Found' if result else 'Not found'}")
    return result



# def add_event_comment(event_id: int, user_id: int, content: str) -> int:
#     """Add a comment to an event.

#     Args:
#         event_id: ID of the event to comment on.
#         user_id: ID of the user posting the comment.
#         content: The content of the comment.

#     Returns:
#         int: ID of the newly created comment.
#     """
#     logger.info(f"Adding comment to event ID: {event_id} by user ID: {user_id}")
#     query = """
#     INSERT INTO event_comments (event_id, user_id, content)
#     VALUES (%s, %s, %s)
#     """
#     result = execute_query(query, (event_id, user_id, content), fetch_one=True)
#     comment_id = result['id'] if result else None
#     logger.info(f"Added comment with ID: {comment_id} to event ID: {event_id}")
#     return comment_id

# ===== New Image Functions (Preferred) =====

def get_event_images_list(event_id: int) -> List[Dict[str, Any]]:
    """Get all images for an event

    Args:
        event_id: ID of the event

    Returns:
        List of image dictionaries
    """
    query = """
    SELECT id, event_id, image_filename, caption, is_primary, created_at, updated_at
    FROM event_images
    WHERE event_id = %s
    ORDER BY created_at DESC
    """
    return execute_query(query, (event_id,), fetch_all=True)

def get_event_image_by_id(image_id: int) -> Optional[Dict[str, Any]]:
    """Get an event image by ID

    Args:
        image_id: ID of the image

    Returns:
        Image dictionary or None if not found
    """
    query = """
    SELECT id, event_id, image_filename, caption, is_primary, created_at, updated_at
    FROM event_images
    WHERE id = %s
    """
    return execute_query(query, (image_id,), fetch_one=True)

def add_event_image_simple(event_id: int, filename: str, user_id: int) -> Optional[Dict[str, Any]]:
    """Add an image to an event

    Args:
        event_id: ID of the event
        filename: Name of the image file
        user_id: ID of the user adding the image

    Returns:
        Newly created image dictionary or None if failed
    """
    # Check if this is the first image for the event (make it primary)
    existing_images = count_event_images(event_id)
    is_primary = (existing_images == 0)

    # Add image to event_images table
    image_id = add_event_image(
        event_id=event_id,
        image_filename=filename,
        caption=None,
        is_primary=is_primary
    )

    # Return the image info
    return get_event_image(image_id)

def delete_event_image_by_id(image_id: int) -> bool:
    """Delete an event image

    Args:
        image_id: ID of the image

    Returns:
        True if successful, False otherwise
    """
    query = """
    DELETE FROM event_images
    WHERE id = %s
    """
    try:
        execute_query(query, (image_id,))
        return True
    except Exception as e:
        logger.error(f"Error deleting event image {image_id}: {str(e)}")
        return False

