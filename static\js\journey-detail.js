/**
 * Journey Detail Page Module
 * Handles journey detail page specific functionality
 */

// Global variables from server data attributes
let JOURNEY_USER_ID, SESSION_USER_ID, IS_STAFF;

/**
 * Initialize global variables from page data
 */
function initializePageData() {
    const pageData = document.getElementById('pageData');
    if (!pageData) {
        console.warn('Page data element not found');
        return;
    }
    
    JOURNEY_USER_ID = parseInt(pageData.dataset.journeyUserId);
    SESSION_USER_ID = pageData.dataset.sessionUserId ? parseInt(pageData.dataset.sessionUserId) : null;
    IS_STAFF = pageData.dataset.isStaff === 'true';
}

/**
 * Smart back button function
 */
function smartBack() {
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
        try {
            const decodedUrl = decodeURIComponent(backUrl);
            if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
                window.location.href = decodedUrl;
                return;
            }
        } catch (e) {
            console.warn('Invalid back URL:', e);
        }
    }

    if (window.history.length > 1) {
        window.history.back();
        return;
    }

    // Fallback to journeys page using data from page data
    const pageData = document.getElementById('pageData');
    const fallbackUrl = pageData?.dataset.backUrl || '/journey/public';
    window.location.href = fallbackUrl;
}

/**
 * Update back button text based on referrer or URL parameters
 */
function updateBackButtonText() {
    const backButtonText = document.getElementById('backButtonText');
    if (!backButtonText) return;

    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
        if (backUrl.includes('/journey/public') || backUrl.includes('/discovery')) {
            backButtonText.textContent = 'Back to Discovery';
        } else if (backUrl.includes('/journey/private')) {
            backButtonText.textContent = 'Back to My Journeys';
        } else if (backUrl.includes('/journey/manage')) {
            backButtonText.textContent = 'Back to Management';
        } else if (backUrl.includes('/published_journey')) {
            backButtonText.textContent = 'Back to Published Journeys';
        } else {
            backButtonText.textContent = 'Back';
        }
        return;
    }

    const referrer = document.referrer;
    if (referrer && referrer.includes('/journey/public')) {
        backButtonText.textContent = 'Back to Discovery';
    } else if (referrer && referrer.includes('/journey/private')) {
        backButtonText.textContent = 'Back to My Journeys';
    } else if (referrer && referrer.includes('/journey/manage')) {
        backButtonText.textContent = 'Back to Management';
    } else {
        backButtonText.textContent = 'Back';
    }
}

/**
 * Timeline adjustment utility
 */
function adjustTimelineLine() {
    const timeline = document.querySelector('.timeline');
    const container = document.querySelector('.timeline-container');
    if (timeline && container) {
        timeline.style.minHeight = '';
    }
}

/**
 * Show cover image modal
 * @param {string} imageUrl - URL of the image
 * @param {string} title - Title for the modal
 */
function showCoverImageModal(imageUrl, title) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('coverImageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal fade image-modal';
        modal.id = 'coverImageModal';
        modal.tabIndex = -1;
        modal.setAttribute('aria-labelledby', 'coverImageModalLabel');
        modal.setAttribute('aria-hidden', 'true');
        
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="coverImageModalLabel">${title || 'Cover Image'}</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <img src="${imageUrl}" alt="${title || 'Cover Image'}" class="img-fluid">
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    } else {
        // Update existing modal
        const modalTitle = modal.querySelector('.modal-title');
        const modalImage = modal.querySelector('.modal-body img');
        if (modalTitle) modalTitle.textContent = title || 'Cover Image';
        if (modalImage) {
            modalImage.src = imageUrl;
            modalImage.alt = title || 'Cover Image';
        }
    }
    
    // Show modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

/**
 * Show protected journey message
 */
function showProtectedJourneyMessage() {
    // Create a simple alert or toast message
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1060; max-width: 400px;';
    alertDiv.innerHTML = `
        <i class="bi bi-shield-lock me-2"></i>
        <strong>Protected Journey:</strong> This journey is protected from staff edits by the owner.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Initialize page when DOM is loaded
 */
function initializePage() {
    initializePageData();
    updateBackButtonText();

    // Initialize maps if available
    if (typeof initializeJourneyMap === 'function') {
        initializeJourneyMap();
    }

    // Add event listener for edit history button
    document.addEventListener('click', function(event) {
        const target = event.target.closest('[data-action="view-edit-history"]');
        if (target) {
            event.preventDefault();
            const journeyId = target.dataset.journeyId;
            const journeyTitle = target.dataset.journeyTitle;
            if (typeof openEditHistoryModal === 'function') {
                openEditHistoryModal(journeyId, journeyTitle);
            }
        }
    });

    adjustTimelineLine();
}

/**
 * Handle window resize
 */
function handleResize() {
    adjustTimelineLine();
}

// Event listeners
document.addEventListener('DOMContentLoaded', initializePage);
window.addEventListener('load', adjustTimelineLine);
window.addEventListener('resize', handleResize);

// Make functions globally available
window.smartBack = smartBack;
window.showCoverImageModal = showCoverImageModal;
window.showProtectedJourneyMessage = showProtectedJourneyMessage;
