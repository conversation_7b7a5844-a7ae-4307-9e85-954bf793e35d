/**
 * Edit History Modal Styles
 * Modern, minimalistic design for edit history display
 */

/* Edit History Modal */
#editHistoryModal .modal-dialog {
  max-width: 900px;
}

#editHistoryModal .modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

#editHistoryModal .modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  border-radius: 16px 16px 0 0;
  padding: 1.5rem 2rem 1rem 2rem;
}

#editHistoryModal .modal-title {
  color: #495057;
  font-weight: 600;
  font-size: 1.25rem;
}

#editHistoryModal .modal-body {
  padding: 1.5rem 2rem 2rem 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Modern Timeline Design */
.edit-timeline {
  position: relative;
  padding: 0;
  margin: 0;
}

.edit-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 20px;
  width: 2px;
  background: linear-gradient(to bottom, #007bff, #6c757d);
  border-radius: 1px;
}

.edit-timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 60px;
}

.edit-timeline-item:last-child {
  margin-bottom: 0;
}

.edit-timeline-badge {
  position: absolute;
  top: 0;
  left: 8px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border: 3px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  z-index: 2;
}

.edit-timeline-badge i {
  font-size: 10px;
  color: white;
}

.edit-timeline-panel {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
}

.edit-timeline-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.edit-timeline-panel::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #e9ecef;
}

.edit-timeline-panel::after {
  content: '';
  position: absolute;
  top: 13px;
  left: -7px;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-right: 7px solid #fff;
}

/* Timeline Content */
.edit-timeline-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
}

.edit-timeline-title {
  font-size: 1rem;
  font-weight: 600;
  color: #212529;
  margin: 0 0 0.5rem 0;
}

.edit-timeline-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
}

.edit-timeline-meta i {
  font-size: 0.75rem;
}

.edit-timeline-body {
  margin-bottom: 1rem;
}

.edit-reason {
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 0.75rem 1rem;
  border-radius: 0 8px 8px 0;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.edit-reason strong {
  color: #495057;
  font-weight: 600;
}

/* Changes Table */
.changes-section {
  margin-top: 1rem;
}

.changes-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.changes-title i {
  color: #007bff;
}

.changes-table {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  font-size: 0.875rem;
}

.changes-table th {
  background: #f8f9fa;
  border: none;
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.changes-table td {
  border: none;
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-top: 1px solid #f1f3f4;
}

.changes-table .field-name {
  font-weight: 500;
  color: #495057;
}

.changes-table .old-value {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
}

.changes-table .new-value {
  color: #198754;
  background: rgba(25, 135, 84, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
}

/* Empty State */
.edit-history-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.edit-history-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.edit-history-empty h4 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.edit-history-empty p {
  margin: 0;
  font-size: 0.9rem;
}

/* Premium Upgrade Section */
.premium-upgrade {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  margin-top: 1rem;
}

.premium-upgrade i {
  font-size: 2.5rem;
  color: #f39c12;
  margin-bottom: 1rem;
}

.premium-upgrade h4 {
  color: #856404;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.premium-upgrade p {
  color: #856404;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.premium-upgrade .btn {
  background: #f39c12;
  border-color: #f39c12;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.premium-upgrade .btn:hover {
  background: #e67e22;
  border-color: #e67e22;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

/* Loading State */
.edit-history-loading {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.edit-history-loading .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  #editHistoryModal .modal-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  #editHistoryModal .modal-header,
  #editHistoryModal .modal-body {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .edit-timeline-item {
    padding-left: 50px;
  }

  .edit-timeline::before {
    left: 16px;
  }

  .edit-timeline-badge {
    left: 4px;
    width: 20px;
    height: 20px;
  }

  .edit-timeline-badge i {
    font-size: 8px;
  }

  .edit-timeline-panel {
    padding: 1rem;
  }

  .changes-table {
    font-size: 0.8rem;
  }

  .changes-table th,
  .changes-table td {
    padding: 0.5rem 0.75rem;
  }
}
