/**
 * Edit History Module
 * Handles edit history modal functionality
 */

/**
 * Open edit history modal for a journey
 * @param {number} journeyId - Journey ID
 * @param {string} journeyTitle - Journey title for display
 */
function openEditHistoryModal(journeyId, journeyTitle) {
    if (!journeyId) {
        console.error('Journey ID is required');
        return;
    }

    // Show loading state
    showEditHistoryModal(journeyTitle, '<div class="edit-history-loading"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p>Loading edit history...</p></div>');

    // Fetch edit history data
    fetch(`/edit-history/api/journey/${journeyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const content = generateEditHistoryContent(data);
                updateEditHistoryModal(content);
            } else {
                showEditHistoryError(data.message || 'Failed to load edit history');
            }
        })
        .catch(error => {
            console.error('Error fetching edit history:', error);
            showEditHistoryError('Failed to load edit history. Please try again.');
        });
}

/**
 * Show the edit history modal
 * @param {string} title - Modal title
 * @param {string} content - Modal content HTML
 */
function showEditHistoryModal(title, content) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('editHistoryModal');
    if (!modal) {
        modal = createEditHistoryModal();
        document.body.appendChild(modal);
    }

    // Update modal content
    const modalTitle = modal.querySelector('.modal-title');
    const modalBody = modal.querySelector('.modal-body');
    
    modalTitle.textContent = `Edit History - ${title}`;
    modalBody.innerHTML = content;

    // Show modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

/**
 * Update existing modal content
 * @param {string} content - New content HTML
 */
function updateEditHistoryModal(content) {
    const modal = document.getElementById('editHistoryModal');
    if (modal) {
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = content;
    }
}

/**
 * Show error in edit history modal
 * @param {string} message - Error message
 */
function showEditHistoryError(message) {
    const errorContent = `
        <div class="edit-history-empty">
            <i class="bi bi-exclamation-triangle"></i>
            <h4>Error Loading History</h4>
            <p>${message}</p>
        </div>
    `;
    updateEditHistoryModal(errorContent);
}

/**
 * Create the edit history modal element
 * @returns {HTMLElement} Modal element
 */
function createEditHistoryModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'editHistoryModal';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'editHistoryModalLabel');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-clock-history text-primary me-2"></i>
                        <h5 class="modal-title" id="editHistoryModalLabel">Edit History</h5>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    `;

    return modal;
}

/**
 * Generate edit history content HTML
 * @param {Object} data - Edit history data from API
 * @returns {string} HTML content
 */
function generateEditHistoryContent(data) {
    if (!data.has_history) {
        return `
            <div class="edit-history-empty">
                <i class="bi bi-clock-history"></i>
                <h4>No Edit History</h4>
                <p>This journey hasn't been edited yet. Any future changes made by staff will appear here.</p>
            </div>
        `;
    }

    let content = '';

    // Show premium upgrade if not premium/staff and has limited history
    if (!data.is_premium && !data.is_staff && data.edit_history.length > 0) {
        content += `
            <div class="premium-upgrade">
                <i class="bi bi-star-fill"></i>
                <h4>Premium Feature</h4>
                <p>Upgrade to Premium to access complete edit history for all your content.</p>
                <a href="/account/profile?active_tab=subscription" class="btn">
                    <i class="bi bi-star me-2"></i>Upgrade to Premium
                </a>
            </div>
        `;
    }

    // Generate timeline
    content += '<div class="edit-timeline">';
    
    data.edit_history.forEach((edit, index) => {
        content += `
            <div class="edit-timeline-item">
                <div class="edit-timeline-badge">
                    <i class="bi bi-pencil"></i>
                </div>
                <div class="edit-timeline-panel">
                    <div class="edit-timeline-header">
                        <h6 class="edit-timeline-title">Edit by ${escapeHtml(edit.editor_username)}</h6>
                        <div class="edit-timeline-meta">
                            <i class="bi bi-clock"></i>
                            <span>${edit.created_at}</span>
                        </div>
                    </div>
                    <div class="edit-timeline-body">
                        <div class="edit-reason">
                            <strong>Reason:</strong> ${escapeHtml(edit.reason)}
                        </div>
                        ${generateChangesTable(edit.field_changes)}
                    </div>
                </div>
            </div>
        `;
    });

    content += '</div>';
    return content;
}

/**
 * Generate changes table HTML
 * @param {Array} changes - Array of field changes
 * @returns {string} HTML for changes table
 */
function generateChangesTable(changes) {
    if (!changes || changes.length === 0) {
        return '<p class="text-muted small">No field changes recorded.</p>';
    }

    let table = `
        <div class="changes-section">
            <div class="changes-title">
                <i class="bi bi-list-ul"></i>
                <span>Changes</span>
            </div>
            <div class="table-responsive">
                <table class="table changes-table">
                    <thead>
                        <tr>
                            <th>Field</th>
                            <th>Before</th>
                            <th>After</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    changes.forEach(change => {
        table += `
            <tr>
                <td class="field-name">${escapeHtml(change.field_name)}</td>
                <td><span class="old-value">${escapeHtml(change.old_value || 'None')}</span></td>
                <td><span class="new-value">${escapeHtml(change.new_value || 'None')}</span></td>
            </tr>
        `;
    });

    table += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    return table;
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
    if (typeof text !== 'string') {
        return text;
    }
    
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Make functions globally available
window.openEditHistoryModal = openEditHistoryModal;
