<!-- Create Event Modal Content - CSS and JS files are included in the parent page -->

<div class="create-event-modal" id="createEventModal">
  <form method="post" action="{{ url_for('event.create_event', journey_id=journey.id) }}" enctype="multipart/form-data"
    novalidate id="createEventForm" class="needs-validation modern-form">

    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid">

        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-info-circle section-icon"></i>
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-type"></i>
                  Event Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title"
                  value="{{ request.form.get('title', '') }}" required minlength="5" maxlength="50"
                  placeholder="Enter event title" />
                <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="3"
                  placeholder="Describe your event...">{{ request.form.get('description', '') }}</textarea>
                <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
              </div>
            </div>
          </div>

          <!-- Date & Time Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-clock section-icon"></i>
              <span class="section-title">Date & Time</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="startDatetime" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date & Time *
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="startDatetime"
                  name="start_datetime" value="{{ request.form.get('start_datetime', '') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              <div class="form-group">
                <label for="endDatetime" class="modern-label">
                  <i class="bi bi-calendar-check"></i>
                  End Date & Time
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="endDatetime" name="end_datetime"
                  value="{{ request.form.get('end_datetime', '') }}" />
                <div class="invalid-feedback">End date cannot be before start date.</div>
              </div>
            </div>
          </div>

          <!-- Images Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-images section-icon"></i>
              <span class="section-title">Event Images</span>
            </div>

            <div class="form-group">
              <label for="images" class="modern-label">
                <i class="bi bi-camera"></i>
                {% if premium_access %}Images (optional, max {{ file_config.maxFileSizeMB }}MB each, limit 10){% else
                %}Image (optional, max {{ file_config.maxFileSizeMB }}MB){% endif %}
              </label>
              <input type="file" class="modern-input" id="images" name="images" {% if premium_access %}multiple{% endif
                %} accept="{{ file_config.allowedExtensionsHtml }}"
                data-premium="{% if premium_access %}true{% else %}false{% endif %}">
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                {% if premium_access %}
                Upload up to 10 images. Maximum {{ file_config.maxFileSizeMB }}MB each. Allowed formats: {{
                file_config.allowedFormatsText }}.
                {% else %}
                Upload one image. Maximum {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{
                file_config.allowedFormatsText }}.
                {% endif %}
              </div>
              <div class="invalid-feedback" id="imagesFeedback">
                {% if premium_access %}
                Images cannot exceed {{ file_config.maxFileSizeMB }}MB each, and you can only upload up to 10 images.
                {% else %}
                Image cannot exceed {{ file_config.maxFileSizeMB }}MB.
                {% endif %}
              </div>

              <!-- Image Preview Container -->
              <div id="imagePreviewContainer" class="image-preview-grid mt-3" style="display: none;">
                <div class="preview-header">
                  <span class="preview-title">
                    <i class="bi bi-eye"></i>
                    Selected Images
                  </span>
                  <button type="button" class="btn btn-sm btn-outline-secondary"
                    onclick="window.imagePreviewInstance?.clearAll()">
                    <i class="bi bi-x"></i>
                    Clear All
                  </button>
                </div>
                <div id="previewGrid" class="row g-2">
                  <!-- Preview images will be inserted here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Location Search Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-search section-icon"></i>
              <span class="section-title">Search Location</span>
            </div>

            <div class="form-group">
              <label for="locationSearch" class="modern-label">
                    <i class="bi bi-search"></i>
                Search for Location *
              </label>
              <div class="location-search-container">
                <input type="text" class="modern-input" id="locationSearch"
                  data-location-search
                  placeholder="Type location name or address..." />
                <div id="locationSuggestions" class="location-dropdown d-none"></div>
              </div>
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                Search for existing locations or new addresses
              </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="location-results" style="display: none;">
              <div class="results-header">
                <h6 class="mb-0">Search Results</h6>
              </div>
              <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
              </div>
            </div>
          </div>

          <!-- Selected Location Section -->
          <div class="form-section compact" id="selectedLocationSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-geo-alt section-icon"></i>
              <span class="section-title">Selected Location</span>
            </div>

            <div class="selected-location-info">
              <div class="form-group">
                <label for="location" class="modern-label">
                  <i class="bi bi-tag"></i>
                  Location Name *
                </label>
                <input type="text" class="modern-input" id="location" name="location" required readonly />
              <div class="invalid-feedback">Location is required.</div>
              </div>

              <div class="location-actions mt-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" id="changeLocationBtn">
                  <i class="bi bi-arrow-left"></i> Change Location
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary" id="editMapLocationBtn"
                  style="display: none;">
                  <i class="bi bi-geo-alt"></i> Change Map Location
                </button>
              </div>
            </div>
          </div>

          <!-- Map Section -->
          <div class="form-section map-section" id="mapSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-map section-icon"></i>
              <span class="section-title">Map Preview</span>
            </div>

            <div class="location-content">
              <!-- Interactive Map -->
              <div class="map-container desktop-optimized">
                <div id="map" class="modern-map"></div>
              </div>

              <!-- Map Search (for new locations) -->
              <div id="mapSearchGroup" class="form-group mt-3" style="display: none;">
                <label for="mapSearch" class="modern-label">
                  <i class="bi bi-search"></i>
                  Search Address on Map
                </label>
                <div class="map-search-container">
                  <input type="text" class="modern-input" id="mapSearch"
                         data-map-search
                         placeholder="Search for address..." />
                  <div id="mapSuggestions" class="location-dropdown d-none"></div>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Search for address and click on map to set location
                </div>

                <!-- Coordinates Status (Hidden from users) -->
                <div id="coordinatesStatus" class="mt-2" style="display: none !important;">
                  <div class="alert alert-success py-2 px-3 mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    <span id="coordinatesText">Location coordinates set</span>
                  </div>
                </div>

                <!-- New Location Name Input -->
                <div id="newLocationNameGroup" class="mt-3" style="display: none;">
                  <label for="newLocationName" class="modern-label">
                    <i class="bi bi-tag"></i>
                    Name for New Location *
                  </label>
                  <input type="text" class="modern-input" id="newLocationName"
                    placeholder="Enter unique name for this location" />
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    This name will be validated and created when you submit the event
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Hidden coordinate fields -->
      <input type="hidden" id="latitude" name="latitude">
      <input type="hidden" id="longitude" name="longitude">
    </div>
  </form>

  <style>
    /* Event Create Specific Styles */
    .result-type {
      background: #e3f2fd;
      color: #2196f3;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }
  </style>

  <script>
    // Initialize Event Create Form
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('createEventForm');
      if (!form) return;

      // Initialize enhanced form validation
      if (window.EnhancedFormValidation) {
        window.EnhancedFormValidation.initializeModernForm(form, {
          validateOnInput: true,
          validateOnBlur: true,
          showSuccessStates: true
        });
      }

      // Initialize image preview for event images
      const imageInput = document.getElementById('images');
      if (imageInput && window.ImagePreview) {
        const isPremium = imageInput.dataset.premium === 'true';

        window.ImagePreview.initialize('#images', '#imagePreviewContainer', {
          maxFileSize: 5 * 1024 * 1024, // 5MB
          allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
          showFileName: true,
          showFileSize: true,
          maxFiles: isPremium ? 10 : 1,
          onImageLoad: function(file, dataUrl) {
            console.log('Event image loaded:', file.name);
          },
          onError: function(message) {
            console.error('Image preview error:', message);
          }
        });
      }

      // Initialize location operations
      if (window.LocationOperations) {
        window.LocationOperations.initialize({
          searchInput: '#locationSearch',
          resultsContainer: '#searchResults',
          selectedSection: '#selectedLocationSection',
          mapContainer: '#map',
          onLocationSelected: function(location) {
            console.log('Location selected:', location);
          }
        });
      }

      console.log('✅ Event create form initialized');
    });
  </script>
</div>