"""
Edit History Routes

This module handles all routes related to edit history including:
- Viewing edit history for a specific journey or event
- Viewing all edit history for a user's content
- Admin/editor views for edit history
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, session, jsonify
from services import edit_history_service, journey_service, subscription_service, user_service
from utils.security import login_required, content_manager_required

# Create blueprint
edit_history_bp = Blueprint('edit_history', __name__, url_prefix='/edit-history')

@edit_history_bp.route('/journey/<int:journey_id>', methods=['GET'])
@login_required
def get_journey_edit_history(journey_id: int):
    """View edit history for a specific journey"""
    # Get the journey to check permissions
    success, message, journey = journey_service.get_journey(journey_id, session.get('user_id'))
    if not success:
        flash(message, 'danger')
        return redirect(url_for('main.get_user_dashboard'))

    # Check if user has permission to view edit history
    # Either they own the journey or they are an editor/admin
    from utils.permissions import PermissionChecker
    is_owner = journey['user_id'] == session.get('user_id')
    is_staff = PermissionChecker.can_access_edit_history()

    if not (is_owner or is_staff):
        flash('You do not have permission to view this edit history', 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    # Check if owner has premium subscription (if not staff)
    is_premium = True  # Staff always get full access
    if is_owner and not is_staff:
        subscription_status = subscription_service.get_user_subscription_status(session.get('user_id'))
        is_premium = subscription_status.get('is_premium', False)

    # Get edit history
    edit_history = edit_history_service.get_content_edit_history('journey', journey_id)

    # For free users, limit to latest edit only
    if not is_premium and edit_history:
        edit_history = edit_history[:1]  # Only show the most recent edit

    return render_template(
        'edit_history/journey_history.html',
        journey=journey,
        edit_history=edit_history,
        is_premium=is_premium,
        is_staff=is_staff
    )

@edit_history_bp.route('/api/journey/<int:journey_id>', methods=['GET'])
@login_required
def get_journey_edit_history_api(journey_id: int):
    """API endpoint to get edit history for a journey (for modal display)"""
    try:
        # Get the journey to check permissions
        success, message, journey = journey_service.get_journey(journey_id, session.get('user_id'))
        if not success:
            return jsonify({'success': False, 'message': message}), 404

        # Check if user has permission to view edit history
        from utils.permissions import PermissionChecker
        is_owner = journey['user_id'] == session.get('user_id')
        is_staff = PermissionChecker.can_access_edit_history()

        if not (is_owner or is_staff):
            return jsonify({'success': False, 'message': 'You do not have permission to view this edit history'}), 403

        # Check if owner has premium subscription (if not staff)
        is_premium = True  # Staff always get full access
        if is_owner and not is_staff:
            subscription_status = subscription_service.get_user_subscription_status(session.get('user_id'))
            is_premium = subscription_status.get('is_premium', False)

        # Get edit history
        edit_history = edit_history_service.get_content_edit_history('journey', journey_id)

        # For free users, limit to latest edit only
        if not is_premium and edit_history:
            edit_history = edit_history[:1]  # Only show the most recent edit

        # Format the data for JSON response
        formatted_history = []
        for edit in edit_history:
            formatted_edit = {
                'id': edit.get('id'),
                'editor_username': edit.get('editor_username'),
                'created_at': edit.get('created_at').strftime('%B %d, %Y at %I:%M %p') if edit.get('created_at') else '',
                'reason': edit.get('reason', ''),
                'field_changes': []
            }

            # Format field changes
            for change in edit.get('field_changes', []):
                formatted_change = {
                    'field_name': change.get('field_name', '').replace('_', ' ').title(),
                    'old_value': change.get('old_value', ''),
                    'new_value': change.get('new_value', '')
                }
                formatted_edit['field_changes'].append(formatted_change)

            formatted_history.append(formatted_edit)

        return jsonify({
            'success': True,
            'journey': {
                'id': journey['id'],
                'title': journey['title'],
                'user_id': journey['user_id']
            },
            'edit_history': formatted_history,
            'is_premium': is_premium,
            'is_staff': is_staff,
            'has_history': len(formatted_history) > 0
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error fetching edit history: {str(e)}'}), 500

@edit_history_bp.route('/event/<int:event_id>', methods=['GET'])
@login_required
def get_event_edit_history(event_id: int):
    """View edit history for a specific event"""
    # First get the event to check permissions
    from services import event_service
    success, message, event = event_service.get_event(event_id, session.get('user_id'))
    if not success:
        flash(message, 'danger')
        return redirect(url_for('main.get_user_dashboard'))

    # Get the journey to check permissions
    success, message, journey = journey_service.get_journey(event['journey_id'], session.get('user_id'))
    if not success:
        flash(message, 'danger')
        return redirect(url_for('main.get_user_dashboard'))

    # Check if user has permission to view edit history
    # Either they own the journey or they are an editor/admin
    from utils.permissions import PermissionChecker
    is_owner = journey['user_id'] == session.get('user_id')
    is_staff = PermissionChecker.can_access_edit_history()

    if not (is_owner or is_staff):
        flash('You do not have permission to view this edit history', 'danger')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    # Check if owner has premium subscription (if not staff)
    is_premium = True  # Staff always get full access
    if is_owner and not is_staff:
        subscription_status = subscription_service.get_user_subscription_status(session.get('user_id'))
        is_premium = subscription_status.get('is_premium', False)

    # Get edit history
    edit_history = edit_history_service.get_content_edit_history('event', event_id)

    # For free users, limit to latest edit only
    if not is_premium and edit_history:
        edit_history = edit_history[:1]  # Only show the most recent edit

    return render_template(
        'edit_history/event_history.html',
        event=event,
        journey=journey,
        edit_history=edit_history,
        is_premium=is_premium,
        is_staff=is_staff
    )

@edit_history_bp.route('/location/<int:location_id>', methods=['GET'])
@login_required
@content_manager_required
def get_location_edit_history(location_id: int):
    """View edit history for a specific location (admin/editor only)"""
    from services import location_service
    location = location_service.get_location(location_id)
    if not location:
        flash('Location not found', 'danger')
        return redirect(url_for('location.get_locations'))

    # Get edit history
    edit_history = edit_history_service.get_content_edit_history('location', location_id)

    return render_template(
        'edit_history/location_history.html',
        location=location,
        edit_history=edit_history
    )

@edit_history_bp.route('/my-content-edit-history', methods=['GET'])
@login_required
def get_my_content_edit_history():
    """View edit history for all content owned by the current user"""
    # Check if user has premium subscription
    subscription_status = subscription_service.get_user_subscription_status(session.get('user_id'))
    if not subscription_status.get('is_premium'):
        flash('Viewing complete edit history requires a premium subscription', 'warning')
        return redirect(url_for('main.get_user_dashboard'))

    # Get edit history
    edit_history = edit_history_service.get_user_content_edit_history(session.get('user_id'))

    return render_template(
        'edit_history/my_content_history.html',
        edit_history=edit_history
    )

@edit_history_bp.route('/editor/<int:editor_id>', methods=['GET'])
@login_required
@content_manager_required
def get_editor_edit_history(editor_id: int):
    """View edit history for a specific editor (admin/editor only)"""
    # Check if viewing own history or has admin role
    if editor_id != session.get('user_id') and session.get('role') != 'admin':
        flash('You can only view your own edit history', 'danger')
        return redirect(url_for('edit_history.get_editor_edit_history', editor_id=session.get('user_id')))

    # Get user info
    editor = user_service.get_user_by_id(editor_id)
    if not editor:
        flash('Editor not found', 'danger')
        return redirect(url_for('main.get_admin_dashboard'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    limit = 20
    offset = (page - 1) * limit

    # Get edit history
    edit_history = edit_history_service.get_editor_edit_history(editor_id, limit=limit, offset=offset)

    return render_template(
        'edit_history/editor_history.html',
        editor=editor,
        edit_history=edit_history,
        page=page
    )