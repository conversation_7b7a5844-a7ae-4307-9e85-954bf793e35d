/* Location Search and Map Styles */

/* Location Search Container */
.location-search-container,
.map-search-container {
  display: flex;
  align-items: center;
  position: relative;
}

.location-search-container .modern-input,
.map-search-container .modern-input {
  flex: 1;
  margin-bottom: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

/* Search Button */
.search-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  transition: all 0.3s ease;
}

.search-button:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.search-button:focus {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Location Dropdown */
.location-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.location-dropdown.d-none {
  display: none;
}

.location-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
}

.location-dropdown-item:hover {
  background: #f8f9fa;
}

.location-dropdown-item:last-child {
  border-bottom: none;
}

.location-dropdown-item.selected {
  background: #e3f2fd;
}

/* Location Results */
.location-results {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  margin-top: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.results-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  color: #495057;
}

.results-list {
  padding: 0;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
  justify-content: space-between;
}

.result-item:hover {
  background: #f8f9fa;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-icon {
  width: 32px;
  height: 32px;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2196f3;
}

.result-details h6 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.result-details small {
  color: #666;
  font-size: 12px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-actions .btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* Selected Location Info */
.selected-location-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.current-location-display {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.current-location-info {
  margin-bottom: 12px;
}

.location-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.location-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.location-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.change-location-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Map Styles */
.map-container {
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  background: #f8f9fa;
  position: relative;
}

.map-container.desktop-optimized {
  height: 300px;
}

.modern-map {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.map-preview-only {
  pointer-events: none;
}

.map-disabled {
  opacity: 0.7;
  pointer-events: none;
}

/* Map Search Group */
#mapSearchGroup {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

#newLocationNameGroup {
  background: #fff3cd;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #ffeaa7;
}

#coordinatesStatus {
  background: #d4edda;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #c3e6cb;
}

/* Location Choice Modal Styles */
.location-choice-options {
  margin-top: 16px;
}

.choice-description {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.choice-benefits {
  margin-top: 8px;
}

.choice-benefits small {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Responsive Map Styles */
@media (max-width: 768px) {
  .map-container.desktop-optimized {
    height: 250px;
  }

  .location-results {
    max-height: 250px;
  }

  .result-item {
    padding: 10px 12px;
  }

  .result-icon {
    width: 28px;
    height: 28px;
  }

  .result-details h6 {
    font-size: 13px;
  }

  .result-details small {
    font-size: 11px;
  }

  .selected-location-info,
  .current-location-display {
    padding: 12px;
  }

  .location-actions,
  .change-location-actions {
    gap: 6px;
  }

  .location-actions .btn,
  .change-location-actions .btn {
    font-size: 12px;
    padding: 6px 10px;
  }

  #mapSearchGroup,
  #newLocationNameGroup {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .map-container.desktop-optimized {
    height: 200px;
  }

  .location-results {
    max-height: 200px;
  }

  .result-item {
    padding: 8px 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .result-info {
    width: 100%;
  }

  .result-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .selected-location-info,
  .current-location-display {
    padding: 10px;
  }

  .location-actions,
  .change-location-actions {
    flex-direction: column;
    gap: 8px;
  }

  .location-actions .btn,
  .change-location-actions .btn {
    width: 100%;
    justify-content: center;
  }

  #mapSearchGroup,
  #newLocationNameGroup {
    padding: 10px;
  }
}

/* Loading States */
.location-search-container.loading .modern-input,
.map-search-container.loading .modern-input {
  background-image: url("data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23667eea'%3e%3ccircle cx='10' cy='10' r='2'%3e%3canimate attributeName='r' begin='0s' dur='1.8s' values='2;10;2' calcMode='spline' keyTimes='0;0.2;1' keySplines='0.165,0.84,0.44,1;0.3,0.61,0.355,1' repeatCount='indefinite'/%3e%3c/circle%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

/* Map Marker Styles */
.custom-marker {
  background: #667eea;
  border: 3px solid white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.custom-marker.selected {
  background: #e53e3e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
